// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

enum NotificationChannelType {
  EMAIL
  SLACK
  WEBHOOK
  WEBSOCKET
  MICROSOFT_TEAMS
  DISCORD
  PAGERDUTY
  SMS
}

enum NotificationEvent {
  PIPELINE_STARTED
  PIPELINE_COMPLETED
  PIPELINE_FAILED
  JOB_STARTED
  JOB_COMPLETED
  JOB_FAILED
  SYSTEM_ALERT
}

enum PipelineRunStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

enum JobStatus {
  PENDING
  RUNNING
  COMPLETED
  FAILED
  CANCELLED
}

enum AuditAction {
  CREATE
  UPDATE
  DELETE
  VIEW
  EXECUTE
  LOGIN
  LOGOUT
  START
}

enum AuditResource {
  USER
  PROJECT
  PIPELINE
  JOB
  SECRET
  WEBHOOK
  NOTIFICATION
  SYSTEM
  SESSION
}

enum Role {
  ADMIN
  USER
  VIEWER
}

enum Permission {
  READ
  WRITE
  EXECUTE
  ADMIN
}

model User {
  id        String   @id @default(uuid())
  email     String   @unique
  username  String   @unique
  password  String
  name      String?
  firstName String?
  lastName  String?
  avatar    String?
  isActive  Boolean  @default(true)
  settings  Json?
  role      String   @default("user")
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  // Relations
  ownedProjects Project[] @relation("ProjectOwner")
  projectMembers ProjectMember[]
  teamMembers TeamMember[]
  auditLogs AuditLog[]
  activities Activity[]
}

model Project {
  id          String   @id @default(uuid())
  name        String
  description String?
  slug        String?  @unique
  settings    Json?
  timestamp   DateTime?
  serviceName String?
  lastChecked DateTime?
  ownerId     String
  owner       User     @relation("ProjectOwner", fields: [ownerId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  pipelines   Pipeline[]
  members     ProjectMember[]
}

model Team {
  id          String   @id @default(uuid())
  name        String
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  members     TeamMember[]
}

model ProjectMember {
  id        String   @id @default(uuid())
  projectId String
  project   Project  @relation(fields: [projectId], references: [id])
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  role      String   @default("member")
  createdAt DateTime @default(now())

  @@unique([projectId, userId])
}

model TeamMember {
  id        String   @id @default(uuid())
  teamId    String
  team      Team     @relation(fields: [teamId], references: [id])
  userId    String
  user      User     @relation(fields: [userId], references: [id])
  role      String   @default("member")
  createdAt DateTime @default(now())

  @@unique([teamId, userId])
}

model Pipeline {
  id          String   @id @default(uuid())
  name        String
  description String?
  projectId   String
  project     Project  @relation(fields: [projectId], references: [id])
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
  runs        PipelineRun[]
}

model PipelineRun {
  id         String           @id @default(uuid())
  pipelineId String
  pipeline   Pipeline         @relation(fields: [pipelineId], references: [id])
  number     Int
  status     PipelineRunStatus
  startedAt  DateTime
  endedAt    DateTime?
  finishedAt DateTime?
  createdAt  DateTime         @default(now())
  jobs       Job[]
}

model Job {
  id            String     @id @default(uuid())
  pipelineRunId String
  pipelineRun   PipelineRun @relation(fields: [pipelineRunId], references: [id])
  name          String
  status        JobStatus
  startedAt     DateTime
  endedAt       DateTime?
  pipelineId    String?
}

model NotificationChannel {
  id        String               @id @default(uuid())
  type      NotificationChannelType
  config    Json
  userId    String?
  createdAt DateTime             @default(now())
  updatedAt DateTime             @updatedAt
  notifications Notification[]
}

model Notification {
  id        String            @id @default(uuid())
  channelId String
  channel   NotificationChannel @relation(fields: [channelId], references: [id])
  event     NotificationEvent
  payload   Json
  status    String
  name      String?
  type      String?
  message   String?
  error     String?
  sentAt    DateTime?
  events    String[]          @default([])
  createdAt DateTime          @default(now())
  updatedAt DateTime          @updatedAt
}

model AuditLog {
  id         String        @id @default(uuid())
  userId     String?
  user       User?         @relation(fields: [userId], references: [id])
  action     AuditAction
  resource   AuditResource
  resourceId String?
  details    Json?
  metadata   Json?
  ipAddress  String?
  userAgent  String?
  success    Boolean       @default(true)
  duration   Int?
  error      String?
  createdAt  DateTime      @default(now())
}

model Webhook {
  id          String   @id @default(uuid())
  url         String
  events      String[]
  secret      String?
  active      Boolean  @default(true)
  timeout     Int      @default(30000)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  // Relations
  events_rel  WebhookEvent[]
}

model WebhookEvent {
  id        String   @id @default(uuid())
  webhookId String
  webhook   Webhook  @relation(fields: [webhookId], references: [id])
  event     String
  payload   Json
  status    String
  response  Json?
  createdAt DateTime @default(now())
}

model Secret {
  id          String   @id @default(uuid())
  name        String
  key         String
  value       String
  project     String?
  job         String?
  environment String?
  description String?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  @@unique([key, project, job])
}

// Alert and Monitoring Models
model Alert {
  id          String   @id @default(uuid())
  name        String
  description String?
  type        String
  severity    String   @default("medium")
  status      String   @default("active")
  source      String?
  condition   Json?
  conditions  Json
  actions     Json
  labels      Json?
  annotations Json?
  resolvedAt  DateTime?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model AlertRule {
  id          String   @id @default(uuid())
  name        String
  description String?
  severity    String   @default("medium")
  condition   Json?
  conditions  Json
  actions     Json
  labels      Json?
  annotations Json?
  metadata    Json?
  enabled     Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model Dashboard {
  id          String   @id @default(uuid())
  name        String
  description String?
  config      Json
  layout      Json?
  panels      Json?
  metadata    Json?
  isPublic    Boolean  @default(false)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model MonitoringConfig {
  id          String   @id @default(uuid())
  name        String
  type        String
  config      Json
  pipelineId  String?
  metrics     Json?
  logging     Json?
  alerting    Json?
  metadata    Json?
  enabled     Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

// Environment and Promotion Models
model Environment {
  id          String   @id @default(uuid())
  name        String
  description String?
  type        String   @default("development")
  config      Json?
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model PromotionRule {
  id          String   @id @default(uuid())
  name        String
  description String?
  sourceEnv   String
  targetEnv   String
  conditions  Json
  actions     Json
  strategy    String   @default("manual")
  metadata    Json?
  enabled     Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model PromotionRequest {
  id          String   @id @default(uuid())
  ruleId      String?
  pipelineId  String?
  version     String?
  sourceEnv   String
  targetEnv   String
  status      String   @default("pending")
  requestedBy String
  approvedBy  String?
  config      Json?
  startedAt   DateTime?
  completedAt DateTime?
  metadata    Json?
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}

model PromotionHistory {
  id          String   @id @default(uuid())
  requestId   String
  action      String
  status      String
  details     Json?
  executedBy  String?
  createdAt   DateTime @default(now())
}

// Activity Model
model Activity {
  id          String   @id @default(uuid())
  type        String
  action      String
  resource    String
  resourceId  String?
  userId      String?
  user        User?    @relation(fields: [userId], references: [id])
  projectId   String?
  metadata    Json?
  timestamp   DateTime @default(now())
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt
}