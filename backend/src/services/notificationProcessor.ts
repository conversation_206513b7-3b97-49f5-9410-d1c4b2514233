import { Job } from 'bull';
import { logger } from '../utils/logger';
import { DatabaseService } from './database';
import { NotificationService, NotificationPayload } from './notification';
import { QueueService } from './queue';
// Import from Prisma client instead of missing types file
import { NotificationEvent, PipelineRunStatus, JobStatus } from '@prisma/client';

export interface NotificationJobData {
  type: string;
  projectId?: string;
  pipelineId?: string;
  jobId?: string;
  userId?: string;
  data?: any;
  event?: NotificationEvent;
  title?: string;
  message?: string;
}

class NotificationProcessor {
  private static instance: NotificationProcessor;
  private notificationService: NotificationService | null = null;
  private db: any;

  private constructor() {
    // NotificationService requires WebSocketService parameter
    // this.notificationService = NotificationService.getInstance();
    this.db = DatabaseService.getClient();
    this.setupQueueProcessors();
  }

  public static getInstance(): NotificationProcessor {
    if (!NotificationProcessor.instance) {
      NotificationProcessor.instance = new NotificationProcessor();
    }
    return NotificationProcessor.instance;
  }

  private setupQueueProcessors(): void {
    const notificationQueue = QueueService.getNotificationQueue();

    // Process notification jobs
    notificationQueue.process('send-notification', 5, this.processNotificationJob.bind(this));

    // Process pipeline events
    notificationQueue.process('pipeline-created', this.processPipelineCreated.bind(this));
    notificationQueue.process('pipeline-started', this.processPipelineStarted.bind(this));
    notificationQueue.process('pipeline-completed', this.processPipelineCompleted.bind(this));
    notificationQueue.process('pipeline-failed', this.processPipelineFailed.bind(this));

    // Process job events
    notificationQueue.process('job-started', this.processJobStarted.bind(this));
    notificationQueue.process('job-completed', this.processJobCompleted.bind(this));
    notificationQueue.process('job-failed', this.processJobFailed.bind(this));

    // Process project events
    notificationQueue.process('project-created', this.processProjectCreated.bind(this));

    logger.info('Notification queue processors initialized');
  }

  private async processNotificationJob(job: Job<NotificationJobData>): Promise<void> {
    const { data } = job;
    
    try {
      logger.info('Processing notification job', { jobId: job.id, type: data.type });

      // If it's a direct notification with payload
      if (data.event && data.title && data.message) {
        const payload: NotificationPayload = {
          event: data.event,
          title: data.title,
          message: data.message,
          data: data.data,
          projectId: data.projectId,
          pipelineId: data.pipelineId,
          jobId: data.jobId,
          userId: data.userId
        };

        await this.sendNotificationsForEvent(payload);
        return;
      }

      // Handle legacy notification types
      switch (data.type) {
        case 'pipeline-created':
          await this.processPipelineCreated(job);
          break;
        case 'pipeline-started':
          await this.processPipelineStarted(job);
          break;
        case 'pipeline-completed':
          await this.processPipelineCompleted(job);
          break;
        case 'pipeline-failed':
          await this.processPipelineFailed(job);
          break;
        case 'job-started':
          await this.processJobStarted(job);
          break;
        case 'job-completed':
          await this.processJobCompleted(job);
          break;
        case 'job-failed':
          await this.processJobFailed(job);
          break;
        case 'project-created':
          await this.processProjectCreated(job);
          break;
        default:
          logger.warn('Unknown notification type', { type: data.type });
      }
    } catch (error) {
      logger.error('Error processing notification job', {
        jobId: job.id,
        type: data.type,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      throw error;
    }
  }

  private async processPipelineCreated(job: Job<NotificationJobData>): Promise<void> {
    const { data } = job;
    
    const pipeline = await this.db.pipeline.findUnique({
      where: { id: data.pipelineId },
      include: { project: true, user: true }
    });

    if (!pipeline) {
      logger.warn('Pipeline not found for notification', { pipelineId: data.pipelineId });
      return;
    }

    const payload: NotificationPayload = {
      event: NotificationEvent.PIPELINE_STARTED,
      title: `Pipeline Created: ${pipeline.name}`,
      message: `A new pipeline "${pipeline.name}" has been created in project "${pipeline.project.name}".`,
      data: {
        pipeline: {
          id: pipeline.id,
          name: pipeline.name,
          slug: pipeline.slug
        },
        project: {
          id: pipeline.project.id,
          name: pipeline.project.name
        },
        user: {
          id: pipeline.user.id,
          username: pipeline.user.username
        }
      },
      projectId: pipeline.projectId,
      pipelineId: pipeline.id,
      userId: pipeline.userId
    };

    await this.sendNotificationsForEvent(payload);
  }

  private async processPipelineStarted(job: Job<NotificationJobData>): Promise<void> {
    const { data } = job;
    
    const pipelineRun = await this.db.pipelineRun.findUnique({
      where: { id: data.data?.runId },
      include: { 
        pipeline: { 
          include: { project: true } 
        } 
      }
    });

    if (!pipelineRun) {
      logger.warn('Pipeline run not found for notification', { runId: data.data?.runId });
      return;
    }

    const payload: NotificationPayload = {
      event: NotificationEvent.PIPELINE_STARTED,
      title: `Pipeline Started: ${pipelineRun.pipeline.name} #${pipelineRun.number}`,
      message: `Pipeline "${pipelineRun.pipeline.name}" run #${pipelineRun.number} has started in project "${pipelineRun.pipeline.project.name}".`,
      data: {
        pipelineRun: {
          id: pipelineRun.id,
          number: pipelineRun.number,
          status: pipelineRun.status
        },
        pipeline: {
          id: pipelineRun.pipeline.id,
          name: pipelineRun.pipeline.name
        },
        project: {
          id: pipelineRun.pipeline.project.id,
          name: pipelineRun.pipeline.project.name
        }
      },
      projectId: pipelineRun.pipeline.projectId,
      pipelineId: pipelineRun.pipelineId
    };

    await this.sendNotificationsForEvent(payload);
  }

  private async processPipelineCompleted(job: Job<NotificationJobData>): Promise<void> {
    const { data } = job;
    
    const pipelineRun = await this.db.pipelineRun.findUnique({
      where: { id: data.data?.runId },
      include: { 
        pipeline: { 
          include: { project: true } 
        } 
      }
    });

    if (!pipelineRun) {
      logger.warn('Pipeline run not found for notification', { runId: data.data?.runId });
      return;
    }

    const payload: NotificationPayload = {
      event: NotificationEvent.PIPELINE_COMPLETED,
      title: `Pipeline Completed: ${pipelineRun.pipeline.name} #${pipelineRun.number}`,
      message: `Pipeline "${pipelineRun.pipeline.name}" run #${pipelineRun.number} has completed successfully in project "${pipelineRun.pipeline.project.name}".`,
      data: {
        pipelineRun: {
          id: pipelineRun.id,
          number: pipelineRun.number,
          status: pipelineRun.status,
          duration: pipelineRun.finishedAt && pipelineRun.startedAt 
            ? pipelineRun.finishedAt.getTime() - pipelineRun.startedAt.getTime()
            : null
        },
        pipeline: {
          id: pipelineRun.pipeline.id,
          name: pipelineRun.pipeline.name
        },
        project: {
          id: pipelineRun.pipeline.project.id,
          name: pipelineRun.pipeline.project.name
        }
      },
      projectId: pipelineRun.pipeline.projectId,
      pipelineId: pipelineRun.pipelineId
    };

    await this.sendNotificationsForEvent(payload);
  }

  private async processPipelineFailed(job: Job<NotificationJobData>): Promise<void> {
    const { data } = job;
    
    const pipelineRun = await this.db.pipelineRun.findUnique({
      where: { id: data.data?.runId },
      include: { 
        pipeline: { 
          include: { project: true } 
        } 
      }
    });

    if (!pipelineRun) {
      logger.warn('Pipeline run not found for notification', { runId: data.data?.runId });
      return;
    }

    const payload: NotificationPayload = {
      event: NotificationEvent.PIPELINE_FAILED,
      title: `Pipeline Failed: ${pipelineRun.pipeline.name} #${pipelineRun.number}`,
      message: `Pipeline "${pipelineRun.pipeline.name}" run #${pipelineRun.number} has failed in project "${pipelineRun.pipeline.project.name}".`,
      data: {
        pipelineRun: {
          id: pipelineRun.id,
          number: pipelineRun.number,
          status: pipelineRun.status,
          error: data.data?.error
        },
        pipeline: {
          id: pipelineRun.pipeline.id,
          name: pipelineRun.pipeline.name
        },
        project: {
          id: pipelineRun.pipeline.project.id,
          name: pipelineRun.pipeline.project.name
        }
      },
      projectId: pipelineRun.pipeline.projectId,
      pipelineId: pipelineRun.pipelineId
    };

    await this.sendNotificationsForEvent(payload);
  }

  private async processJobStarted(job: Job<NotificationJobData>): Promise<void> {
    const { data } = job;

    const jobRecord = await this.db.job.findUnique({
      where: { id: data.jobId },
      include: {
        pipelineRun: {
          include: {
            pipeline: {
              include: { project: true }
            }
          }
        },
        user: true
      }
    });

    if (!jobRecord) {
      logger.warn('Job not found for notification', { jobId: data.jobId });
      return;
    }

    const payload: NotificationPayload = {
      event: NotificationEvent.JOB_STARTED,
      title: `Job Started: ${jobRecord.name}`,
      message: `Job "${jobRecord.name}" has started in pipeline "${jobRecord.pipelineRun.pipeline.name}".`,
      data: {
        job: {
          id: jobRecord.id,
          name: jobRecord.name,
          status: jobRecord.status
        },
        pipelineRun: {
          id: jobRecord.pipelineRun.id,
          number: jobRecord.pipelineRun.number
        },
        pipeline: {
          id: jobRecord.pipelineRun.pipeline.id,
          name: jobRecord.pipelineRun.pipeline.name
        },
        project: {
          id: jobRecord.pipelineRun.pipeline.project.id,
          name: jobRecord.pipelineRun.pipeline.project.name
        }
      },
      projectId: jobRecord.pipelineRun.pipeline.projectId,
      pipelineId: jobRecord.pipelineRun.pipelineId,
      jobId: jobRecord.id,
      userId: jobRecord.userId
    };

    await this.sendNotificationsForEvent(payload);
  }

  private async processJobCompleted(job: Job<NotificationJobData>): Promise<void> {
    const { data } = job;

    const jobRecord = await this.db.job.findUnique({
      where: { id: data.jobId },
      include: {
        pipelineRun: {
          include: {
            pipeline: {
              include: { project: true }
            }
          }
        },
        user: true
      }
    });

    if (!jobRecord) {
      logger.warn('Job not found for notification', { jobId: data.jobId });
      return;
    }

    const payload: NotificationPayload = {
      event: NotificationEvent.JOB_COMPLETED,
      title: `Job Completed: ${jobRecord.name}`,
      message: `Job "${jobRecord.name}" has completed successfully in pipeline "${jobRecord.pipelineRun.pipeline.name}".`,
      data: {
        job: {
          id: jobRecord.id,
          name: jobRecord.name,
          status: jobRecord.status,
          duration: jobRecord.finishedAt && jobRecord.startedAt
            ? jobRecord.finishedAt.getTime() - jobRecord.startedAt.getTime()
            : null
        },
        pipelineRun: {
          id: jobRecord.pipelineRun.id,
          number: jobRecord.pipelineRun.number
        },
        pipeline: {
          id: jobRecord.pipelineRun.pipeline.id,
          name: jobRecord.pipelineRun.pipeline.name
        },
        project: {
          id: jobRecord.pipelineRun.pipeline.project.id,
          name: jobRecord.pipelineRun.pipeline.project.name
        }
      },
      projectId: jobRecord.pipelineRun.pipeline.projectId,
      pipelineId: jobRecord.pipelineRun.pipelineId,
      jobId: jobRecord.id,
      userId: jobRecord.userId
    };

    await this.sendNotificationsForEvent(payload);
  }

  private async processJobFailed(job: Job<NotificationJobData>): Promise<void> {
    const { data } = job;

    const jobRecord = await this.db.job.findUnique({
      where: { id: data.jobId },
      include: {
        pipelineRun: {
          include: {
            pipeline: {
              include: { project: true }
            }
          }
        },
        user: true
      }
    });

    if (!jobRecord) {
      logger.warn('Job not found for notification', { jobId: data.jobId });
      return;
    }

    const payload: NotificationPayload = {
      event: NotificationEvent.JOB_FAILED,
      title: `Job Failed: ${jobRecord.name}`,
      message: `Job "${jobRecord.name}" has failed in pipeline "${jobRecord.pipelineRun.pipeline.name}".`,
      data: {
        job: {
          id: jobRecord.id,
          name: jobRecord.name,
          status: jobRecord.status,
          error: data.data?.error
        },
        pipelineRun: {
          id: jobRecord.pipelineRun.id,
          number: jobRecord.pipelineRun.number
        },
        pipeline: {
          id: jobRecord.pipelineRun.pipeline.id,
          name: jobRecord.pipelineRun.pipeline.name
        },
        project: {
          id: jobRecord.pipelineRun.pipeline.project.id,
          name: jobRecord.pipelineRun.pipeline.project.name
        }
      },
      projectId: jobRecord.pipelineRun.pipeline.projectId,
      pipelineId: jobRecord.pipelineRun.pipelineId,
      jobId: jobRecord.id,
      userId: jobRecord.userId
    };

    await this.sendNotificationsForEvent(payload);
  }

  private async processProjectCreated(job: Job<NotificationJobData>): Promise<void> {
    const { data } = job;

    const project = await this.db.project.findUnique({
      where: { id: data.projectId },
      include: {
        organization: true,
        members: {
          include: { user: true }
        }
      }
    });

    if (!project) {
      logger.warn('Project not found for notification', { projectId: data.projectId });
      return;
    }

    const payload: NotificationPayload = {
      event: NotificationEvent.SYSTEM_ALERT, // PROJECT_CREATED doesn't exist
      title: `Project Created: ${project.name}`,
      message: `A new project "${project.name}" has been created${project.organization ? ` in organization "${project.organization.name}"` : ''}.`,
      data: {
        project: {
          id: project.id,
          name: project.name,
          slug: project.slug,
          description: project.description
        },
        organization: project.organization ? {
          id: project.organization.id,
          name: project.organization.name
        } : null
      },
      projectId: project.id,
      userId: data.userId
    };

    await this.sendNotificationsForEvent(payload);
  }

  public async sendNotificationsForEvent(payload: NotificationPayload): Promise<void> {
    try {
      if (!payload.event || !payload.title || !payload.message) {
        logger.error('Invalid notification payload', { payload });
        throw new Error('Invalid notification payload: missing required fields');
      }

      // Get all active notification rules for this event
      const rules = await this.db.notificationRule.findMany({
        where: {
          events: { has: payload.event },
          isActive: true,
          OR: [
            { projectId: payload.projectId },
            { projectId: null }
          ]
        },
        include: {
          channel: {
            where: { isActive: true },
            select: {
              id: true,
              name: true,
              type: true,
              config: true,
              projectId: true,
              userId: true
            }
          }
        }
      });

      if (!rules.length) {
        logger.debug('No active notification rules found for event', {
          event: payload.event,
          projectId: payload.projectId
        });
        return;
      }

      // Process each rule
      for (const rule of rules) {
        try {
          // Skip if channel is not active or not found
          if (!rule.channel || !rule.channel.isActive) {
            logger.debug('Skipping inactive channel', {
              ruleId: rule.id,
              channelId: rule.channelId
            });
            continue;
          }

          // Check conditions if they exist
          if (rule.conditions && !this.evaluateConditions(rule.conditions, payload)) {
            logger.debug('Conditions not met for rule', {
              ruleId: rule.id,
              conditions: rule.conditions
            });
            continue;
          }

          // Send notification
          const result = await this.notificationService?.sendNotification(
            rule.channel.id,
            payload
          );

          if (!result?.success) {
            logger.error('Failed to send notification', {
              ruleId: rule.id,
              channelId: rule.channel.id,
              error: result?.error
            });
          }
        } catch (error) {
          logger.error('Error processing notification rule', {
            ruleId: rule.id,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          // Continue with next rule even if one fails
          continue;
        }
      }
    } catch (error) {
      logger.error('Error in sendNotificationsForEvent', {
        error: error instanceof Error ? error.message : 'Unknown error',
        payload
      });
      throw error;
    }
  }

  private evaluateConditions(conditions: any, payload: NotificationPayload): boolean {
    try {
      // Simple condition evaluation
      // This can be extended to support more complex conditions

      if (conditions.branch && payload.data?.branch) {
        const branchCondition = conditions.branch;
        if (typeof branchCondition === 'string') {
          return payload.data.branch === branchCondition;
        }
        if (branchCondition.includes && Array.isArray(branchCondition.includes)) {
          return branchCondition.includes.includes(payload.data.branch);
        }
        if (branchCondition.excludes && Array.isArray(branchCondition.excludes)) {
          return !branchCondition.excludes.includes(payload.data.branch);
        }
      }

      if (conditions.status && payload.data?.status) {
        const statusCondition = conditions.status;
        if (typeof statusCondition === 'string') {
          return payload.data.status === statusCondition;
        }
        if (statusCondition.includes && Array.isArray(statusCondition.includes)) {
          return statusCondition.includes.includes(payload.data.status);
        }
      }

      if (conditions.user && payload.userId) {
        const userCondition = conditions.user;
        if (typeof userCondition === 'string') {
          return payload.userId === userCondition;
        }
        if (userCondition.includes && Array.isArray(userCondition.includes)) {
          return userCondition.includes.includes(payload.userId);
        }
      }

      // If no conditions are specified or none match, allow the notification
      return true;
    } catch (error) {
      logger.error('Error evaluating notification conditions', {
        conditions,
        error: error instanceof Error ? error.message : 'Unknown error'
      });
      // Default to allowing notification if condition evaluation fails
      return true;
    }
  }

  // Public methods for triggering notifications
  public async triggerPipelineNotification(
    event: NotificationEvent,
    pipelineId: string,
    runId?: string,
    data?: any
  ): Promise<void> {
    await QueueService.addNotification({
      type: event.toLowerCase().replace(/_/g, '-'),
      pipelineId,
      data: { runId, ...data }
    });
  }

  public async triggerJobNotification(
    event: NotificationEvent,
    jobId: string,
    data?: any
  ): Promise<void> {
    await QueueService.addNotification({
      type: event.toLowerCase().replace(/_/g, '-'),
      jobId,
      data
    });
  }

  public async triggerProjectNotification(
    event: NotificationEvent,
    projectId: string,
    userId?: string,
    data?: any
  ): Promise<void> {
    await QueueService.addNotification({
      type: event.toLowerCase().replace(/_/g, '-'),
      projectId,
      userId,
      data
    });
  }

  public async triggerCustomNotification(payload: NotificationPayload): Promise<void> {
    await QueueService.addNotification({
      type: 'send-notification',
      event: payload.event,
      title: payload.title,
      message: payload.message,
      data: payload.data,
      projectId: payload.projectId,
      pipelineId: payload.pipelineId,
      jobId: payload.jobId,
      userId: payload.userId
    });
  }
}

export { NotificationProcessor };
