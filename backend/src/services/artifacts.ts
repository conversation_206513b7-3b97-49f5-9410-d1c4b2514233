import { S3 } from 'aws-sdk';
import { createReadStream, createWriteStream } from 'fs';
import { mkdir, unlink } from 'fs/promises';
import { join } from 'path';
import { pipeline } from 'stream/promises';
import { logger } from '../lib/logger';

interface ArtifactConfig {
  bucket: string;
  region: string;
  accessKeyId: string;
  secretAccessKey: string;
}

export class ArtifactService {
  private static instance: ArtifactService;
  private s3: S3;
  private config: ArtifactConfig;

  private constructor(config: ArtifactConfig) {
    this.config = config;
    this.s3 = new S3({
      region: config.region,
      accessKeyId: config.accessKeyId,
      secretAccessKey: config.secretAccessKey,
    });
  }

  public static getInstance(config?: ArtifactConfig): ArtifactService {
    if (!ArtifactService.instance && config) {
      ArtifactService.instance = new ArtifactService(config);
    }
    return ArtifactService.instance;
  }

  /**
   * Upload an artifact to S3
   */
  async uploadArtifact(
    jobId: string,
    artifactPath: string,
    artifactName: string
  ): Promise<string> {
    try {
      const key = `artifacts/${jobId}/${artifactName}`;
      
      await this.s3
        .upload({
          Bucket: this.config.bucket,
          Key: key,
          Body: createReadStream(artifactPath),
        })
        .promise();

      return key;
    } catch (error) {
      logger.error('Failed to upload artifact:', error);
      throw new Error('Failed to upload artifact');
    }
  }

  /**
   * Download an artifact from S3
   */
  async downloadArtifact(
    jobId: string,
    artifactName: string,
    destinationPath: string
  ): Promise<void> {
    try {
      const key = `artifacts/${jobId}/${artifactName}`;
      
      // Ensure destination directory exists
      await mkdir(destinationPath, { recursive: true });
      
      const filePath = join(destinationPath, artifactName);
      
      const response = await this.s3
        .getObject({
          Bucket: this.config.bucket,
          Key: key,
        })
        .promise();

      if (!response.Body) {
        throw new Error('No artifact content received');
      }

      await pipeline(
        response.Body as NodeJS.ReadableStream,
        createWriteStream(filePath)
      );
    } catch (error) {
      logger.error('Failed to download artifact:', error);
      throw new Error('Failed to download artifact');
    }
  }

  /**
   * List artifacts for a job
   */
  async listArtifacts(jobId: string): Promise<string[]> {
    try {
      const response = await this.s3
        .listObjectsV2({
          Bucket: this.config.bucket,
          Prefix: `artifacts/${jobId}/`,
        })
        .promise();

      return (response.Contents || []).map((item: any) => item.Key || '');
    } catch (error) {
      logger.error('Failed to list artifacts:', error);
      throw new Error('Failed to list artifacts');
    }
  }

  /**
   * Delete an artifact
   */
  async deleteArtifact(jobId: string, artifactName: string): Promise<void> {
    try {
      const key = `artifacts/${jobId}/${artifactName}`;
      
      await this.s3
        .deleteObject({
          Bucket: this.config.bucket,
          Key: key,
        })
        .promise();
    } catch (error) {
      logger.error('Failed to delete artifact:', error);
      throw new Error('Failed to delete artifact');
    }
  }

  /**
   * Get artifact metadata
   */
  async getArtifactMetadata(jobId: string, artifactName: string): Promise<any> {
    try {
      const key = `artifacts/${jobId}/${artifactName}`;
      
      const response = await this.s3
        .headObject({
          Bucket: this.config.bucket,
          Key: key,
        })
        .promise();

      return {
        size: response.ContentLength,
        lastModified: response.LastModified,
        contentType: response.ContentType,
        metadata: response.Metadata,
      };
    } catch (error) {
      logger.error('Failed to get artifact metadata:', error);
      throw new Error('Failed to get artifact metadata');
    }
  }
} 