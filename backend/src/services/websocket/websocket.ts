import WebSocket from 'ws';
import { Server } from 'http';
import { EventEmitter } from 'events';
import { MetricsManager } from '../../observability/metrics';
import { LogManager } from '../../observability/logs';

export interface PipelineEvent {
  type: 'pipeline_start' | 'pipeline_complete' | 'job_start' | 'job_complete' | 'job_failed' | 'log_update' | 'metrics_update';
  data: any;
}

export class WebSocketService extends EventEmitter {
  private wss: WebSocket.Server;
  private clients: Map<string, Set<WebSocket>> = new Map();
  private metricsManager: MetricsManager;
  private logManager: LogManager;

  constructor(server: Server) {
    super();
    this.wss = new WebSocket.Server({ server });
    this.metricsManager = MetricsManager.getInstance();
    this.logManager = LogManager.getInstance();
    this.setupWebSocketServer();
  }

  private setupWebSocketServer() {
    this.wss.on('connection', (ws: WebSocket, req: any) => {
      const path = req.url;
      const pipelineId = path.match(/\/pipelines\/([^\/]+)/)?.[1];
      const jobId = path.match(/\/jobs\/([^\/]+)/)?.[1];

      if (pipelineId) {
        this.addClient('pipeline', pipelineId, ws);
      }
      if (jobId) {
        this.addClient('job', jobId, ws);
      }

      ws.on('close', () => {
        if (pipelineId) {
          this.removeClient('pipeline', pipelineId, ws);
        }
        if (jobId) {
          this.removeClient('job', jobId, ws);
        }
      });

      ws.on('error', (error) => {
        console.error('WebSocket error:', error);
        if (pipelineId) {
          this.removeClient('pipeline', pipelineId, ws);
        }
        if (jobId) {
          this.removeClient('job', jobId, ws);
        }
      });
    });
  }

  private addClient(type: 'pipeline' | 'job', id: string, ws: WebSocket) {
    const key = `${type}:${id}`;
    if (!this.clients.has(key)) {
      this.clients.set(key, new Set());
    }
    this.clients.get(key)!.add(ws);
  }

  private removeClient(type: 'pipeline' | 'job', id: string, ws: WebSocket) {
    const key = `${type}:${id}`;
    const clients = this.clients.get(key);
    if (clients) {
      clients.delete(ws);
      if (clients.size === 0) {
        this.clients.delete(key);
      }
    }
  }

  public broadcast(event: PipelineEvent) {
    const message = JSON.stringify(event);
    this.clients.forEach((clients, key) => {
      clients.forEach((client) => {
        if (client.readyState === WebSocket.OPEN) {
          client.send(message);
        }
      });
    });
  }

  public sendPipelineUpdate(pipelineId: string, status: string, data: any) {
    this.broadcast({
      type: 'pipeline_start',
      data: {
        pipelineId,
        status,
        ...data
      }
    });
  }

  public sendJobUpdate(pipelineId: string, jobId: string, status: string, data: any) {
    this.broadcast({
      type: 'job_start',
      data: {
        pipelineId,
        jobId,
        status,
        ...data
      }
    });
  }

  public sendLogUpdate(pipelineId: string, jobId: string, log: string) {
    this.broadcast({
      type: 'log_update',
      data: {
        pipelineId,
        jobId,
        log
      }
    });
  }

  public sendMetricsUpdate(pipelineId: string, jobId: string, metrics: any) {
    this.broadcast({
      type: 'metrics_update',
      data: {
        pipelineId,
        jobId,
        metrics
      }
    });
  }

  public async handleJobLogs(ws: WebSocket, jobId: string) {
    try {
      const logs = await this.logManager.getJobLogs(jobId);
      ws.send(JSON.stringify({
        type: 'log_update',
        data: {
          jobId,
          logs
        }
      }));

      // Subscribe to new logs
      this.logManager.on('new_log', (log) => {
        if (log.jobId === jobId) {
          ws.send(JSON.stringify({
            type: 'log_update',
            data: {
              jobId,
              log
            }
          }));
        }
      });
    } catch (error) {
      console.error('Error handling job logs:', error);
      ws.close();
    }
  }

  public async handleJobMetrics(ws: WebSocket, jobId: string) {
    try {
      const metrics = await this.metricsManager.getJobMetrics(jobId);
      ws.send(JSON.stringify({
        type: 'metrics_update',
        data: {
          jobId,
          metrics
        }
      }));

      // Subscribe to metric updates
      (this.metricsManager as any).on?.('metric_update', (metric: any) => {
        if (metric.jobId === jobId) {
          ws.send(JSON.stringify({
            type: 'metrics_update',
            data: {
              jobId,
              metric
            }
          }));
        }
      });
    } catch (error) {
      console.error('Error handling job metrics:', error);
      ws.close();
    }
  }
} 