import { DeploymentConfig } from '../types';

export const kubernetesDeploymentConfig: DeploymentConfig = {
  platform: 'KUBERNETES' as any,
  strategy: 'ROLLING_UPDATE' as any,
  replicas: 3,
  resources: {
    cpu: '1000m',
    memory: '1Gi',
    storage: '10Gi',
  },
  environment: {
    variables: {
      NODE_ENV: 'production',
    },
    secrets: [],
  },
  rollback: {
    automatic: false,
    onFailure: true,
    maxRetries: 3,
  },
};