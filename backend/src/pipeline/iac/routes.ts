import { Router } from 'express';
import { PrismaClient } from '@prisma/client';
import { IaCController } from './controller';
import { IaCService } from './service';
import { authenticate } from '../../middleware/auth';

const router = Router();
const prisma = new PrismaClient();
const service = new IaCService(prisma);
const controller = new IaCController(service);

// Create a new IaC module
router.post('/modules', authenticate, async (req, res) => {
  await controller.createModule(req, res);
});

// Get a specific IaC module
router.get('/modules/:moduleId', authenticate, async (req, res) => {
  await controller.getModule(req, res);
});

// Execute an IaC module
router.post('/modules/:moduleId/execute', authenticate, async (req, res) => {
  await controller.executeModule(req, res);
});

// Get the current state of an IaC module
router.get('/modules/:moduleId/state/:environmentId', authenticate, async (req, res) => {
  await controller.getState(req, res);
});

// Detect drift in an IaC module
router.post('/modules/:moduleId/drift/:environmentId', authenticate, async (req, res) => {
  await controller.detectDrift(req, res);
});

export default router; 