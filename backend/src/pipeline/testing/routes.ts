import { Router } from 'express';
import { TestController } from './controller';
import { TestService } from './service';
import { PrismaClient } from '@prisma/client';
import { authenticate } from '../../middleware/auth';

const router = Router();
const prisma = new PrismaClient();
const testService = new TestService(prisma);
const testController = new TestController(testService);

// Create a new test suite
router.post('/', authenticate, async (req, res) => {
  await testController.createTestSuite(req, res);
});

// Create a new test case in a suite
router.post('/:suiteId/cases', authenticate, async (req, res) => {
  await testController.createTestCase(req, res);
});

// Execute a test suite
router.post('/:suiteId/execute', authenticate, async (req, res) => {
  await testController.executeTestSuite(req, res);
});

// Get test report for a suite
router.get('/:suiteId/report', authenticate, async (req, res) => {
  await testController.getTestReport(req, res);
});

export default router; 