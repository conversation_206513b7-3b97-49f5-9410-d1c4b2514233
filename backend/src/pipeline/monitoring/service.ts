import { EventEmitter } from 'events';
import { PrismaClient } from '@prisma/client';
import {
  Metric,
  MetricQuery,
  LogEntry,
  LogQuery,
  Alert,
  AlertRule,
  Dashboard,
  MonitoringConfig,
  MetricType,
  LogLevel,
  AlertSeverity,
  AlertStatus
} from './types';
import {
  MetricValidationError,
  MetricNotFoundError,
  LogValidationError,
  LogNotFoundError,
  AlertValidationError,
  AlertNotFoundError,
  AlertRuleValidationError,
  AlertRuleNotFoundError,
  DashboardValidationError,
  DashboardNotFoundError,
  MonitoringConfigValidationError,
  MonitoringConfigNotFoundError,
  MetricQueryError,
  LogQueryError,
  AlertEvaluationError,
  DashboardRenderError
} from './errors';

export class MonitoringService extends EventEmitter {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
  }

  // Metric Management
  async createMetric(metric: Omit<Metric, 'id' | 'createdAt' | 'updatedAt'>): Promise<Metric> {
    try {
      this.validateMetric(metric);
      // metric model doesn't exist - returning mock data
      const createdMetric: Metric = {
        id: 'mock-metric-' + Date.now(),
        name: metric.name,
        type: metric.type,
        value: metric.value,
        labels: metric.labels,
        timestamp: metric.timestamp,
        metadata: metric.metadata,
      };
      this.emit('metric:created', createdMetric);
      return createdMetric;
    } catch (error) {
      if (error instanceof MetricValidationError) {
        throw error;
      }
      throw new MetricValidationError(`Failed to create metric: ${(error as Error).message}`);
    }
  }

  async getMetrics(query: MetricQuery): Promise<Metric[]> {
    try {
      // metric model doesn't exist - returning mock data
      const metrics: Metric[] = [];
      return metrics;
    } catch (error) {
      throw new MetricQueryError(`Failed to query metrics: ${(error as Error).message}`);
    }
  }

  // Log Management
  async createLogEntry(log: Omit<LogEntry, 'id' | 'createdAt' | 'updatedAt'>): Promise<LogEntry> {
    try {
      this.validateLogEntry(log);
      // logEntry model doesn't exist - returning mock data
      const createdLog: LogEntry = {
        id: 'mock-log-' + Date.now(),
        level: log.level,
        message: log.message,
        source: log.source,
        timestamp: log.timestamp,
        metadata: log.metadata,
      };
      this.emit('log:created', createdLog);
      return createdLog;
    } catch (error) {
      if (error instanceof LogValidationError) {
        throw error;
      }
      throw new LogValidationError(`Failed to create log entry: ${(error as Error).message}`);
    }
  }

  async getLogs(query: LogQuery): Promise<LogEntry[]> {
    try {
      // logEntry model doesn't exist - returning mock data
      const logs: LogEntry[] = [];
      return logs;
    } catch (error) {
      throw new LogQueryError(`Failed to query logs: ${(error as Error).message}`);
    }
  }

  // Alert Management
  async createAlert(alert: Omit<Alert, 'id' | 'createdAt' | 'updatedAt'>): Promise<Alert> {
    try {
      this.validateAlert(alert);
      const createdAlert = await this.prisma.alert.create({
        data: {
          name: alert.name,
          description: alert.description,
          severity: alert.severity,
          status: alert.status,
          source: alert.source,
          condition: alert.condition,
          labels: alert.labels,
          annotations: alert.annotations
        }
      });
      this.emit('alert:created', createdAlert);
      return createdAlert;
    } catch (error) {
      if (error instanceof AlertValidationError) {
        throw error;
      }
      throw new AlertValidationError(`Failed to create alert: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async updateAlertStatus(id: string, status: AlertStatus): Promise<Alert> {
    try {
      const alert = await this.prisma.alert.update({
        where: { id },
        data: {
          status,
          resolvedAt: status === AlertStatus.RESOLVED ? new Date() : undefined
        }
      });
      this.emit('alert:updated', alert);
      return alert;
    } catch (error) {
      throw new AlertNotFoundError(`Failed to update alert status: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Alert Rule Management
  async createAlertRule(rule: Omit<AlertRule, 'id' | 'createdAt' | 'updatedAt'>): Promise<AlertRule> {
    try {
      this.validateAlertRule(rule);
      const createdRule = await this.prisma.alertRule.create({
        data: {
          name: rule.name,
          description: rule.description,
          severity: rule.severity,
          condition: rule.condition,
          labels: rule.labels,
          annotations: rule.annotations,
          enabled: rule.enabled,
          metadata: rule.metadata
        }
      });
      this.emit('alertRule:created', createdRule);
      return createdRule;
    } catch (error) {
      if (error instanceof AlertRuleValidationError) {
        throw error;
      }
      throw new AlertRuleValidationError(`Failed to create alert rule: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async evaluateAlertRules(): Promise<void> {
    try {
      const rules = await this.prisma.alertRule.findMany({
        where: { enabled: true }
      });

      for (const rule of rules) {
        await this.evaluateRule(rule);
      }
    } catch (error) {
      throw new AlertEvaluationError(`Failed to evaluate alert rules: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Dashboard Management
  async createDashboard(dashboard: Omit<Dashboard, 'id' | 'createdAt' | 'updatedAt'>): Promise<Dashboard> {
    try {
      this.validateDashboard(dashboard);
      const createdDashboard = await this.prisma.dashboard.create({
        data: {
          name: dashboard.name,
          description: dashboard.description,
          panels: dashboard.panels,
          metadata: dashboard.metadata
        }
      });
      this.emit('dashboard:created', createdDashboard);
      return createdDashboard;
    } catch (error) {
      if (error instanceof DashboardValidationError) {
        throw error;
      }
      throw new DashboardValidationError(`Failed to create dashboard: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  async renderDashboard(id: string): Promise<any> {
    try {
      const dashboard = await this.prisma.dashboard.findUnique({
        where: { id }
      });

      if (!dashboard) {
        throw new DashboardNotFoundError(`Dashboard not found: ${id}`);
      }

      const renderedPanels = await Promise.all(
        dashboard.panels.map(async (panel: any) => {
          const metrics = await this.getMetrics({
            name: panel.metrics[0],
            startTime: new Date(Date.now() - 24 * 60 * 60 * 1000),
            endTime: new Date()
          });
          return {
            ...panel,
            data: metrics
          };
        })
      );

      return {
        ...dashboard,
        panels: renderedPanels
      };
    } catch (error) {
      throw new DashboardRenderError(`Failed to render dashboard: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Monitoring Configuration
  async createMonitoringConfig(config: Omit<MonitoringConfig, 'id' | 'createdAt' | 'updatedAt'>): Promise<MonitoringConfig> {
    try {
      this.validateMonitoringConfig(config);
      const createdConfig = await this.prisma.monitoringConfig.create({
        data: {
          pipelineId: config.pipelineId,
          metrics: config.metrics,
          logging: config.logging,
          alerting: config.alerting,
          metadata: config.metadata
        }
      });
      this.emit('monitoringConfig:created', createdConfig);
      return createdConfig;
    } catch (error) {
      if (error instanceof MonitoringConfigValidationError) {
        throw error;
      }
      throw new MonitoringConfigValidationError(`Failed to create monitoring config: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // Private validation methods
  private validateMetric(metric: Partial<Metric>): void {
    if (!metric.name) {
      throw new MetricValidationError('Metric name is required');
    }
    if (!metric.type || !Object.values(MetricType).includes(metric.type as MetricType)) {
      throw new MetricValidationError('Invalid metric type');
    }
    if (typeof metric.value !== 'number') {
      throw new MetricValidationError('Metric value must be a number');
    }
  }

  private validateLogEntry(log: Partial<LogEntry>): void {
    if (!log.level || !Object.values(LogLevel).includes(log.level as LogLevel)) {
      throw new LogValidationError('Invalid log level');
    }
    if (!log.message) {
      throw new LogValidationError('Log message is required');
    }
    if (!log.source) {
      throw new LogValidationError('Log source is required');
    }
  }

  private validateAlert(alert: Partial<Alert>): void {
    if (!alert.name) {
      throw new AlertValidationError('Alert name is required');
    }
    if (!alert.severity || !Object.values(AlertSeverity).includes(alert.severity as AlertSeverity)) {
      throw new AlertValidationError('Invalid alert severity');
    }
    if (!alert.status || !Object.values(AlertStatus).includes(alert.status as AlertStatus)) {
      throw new AlertValidationError('Invalid alert status');
    }
    if (!alert.condition) {
      throw new AlertValidationError('Alert condition is required');
    }
  }

  private validateAlertRule(rule: Partial<AlertRule>): void {
    if (!rule.name) {
      throw new AlertRuleValidationError('Alert rule name is required');
    }
    if (!rule.severity || !Object.values(AlertSeverity).includes(rule.severity as AlertSeverity)) {
      throw new AlertRuleValidationError('Invalid alert rule severity');
    }
    if (!rule.condition) {
      throw new AlertRuleValidationError('Alert rule condition is required');
    }
  }

  private validateDashboard(dashboard: Partial<Dashboard>): void {
    if (!dashboard.name) {
      throw new DashboardValidationError('Dashboard name is required');
    }
    if (!dashboard.panels || !Array.isArray(dashboard.panels)) {
      throw new DashboardValidationError('Dashboard panels must be an array');
    }
  }

  private validateMonitoringConfig(config: Partial<MonitoringConfig>): void {
    if (!config.pipelineId) {
      throw new MonitoringConfigValidationError('Pipeline ID is required');
    }
    if (!config.metrics) {
      throw new MonitoringConfigValidationError('Metrics configuration is required');
    }
    if (!config.logging) {
      throw new MonitoringConfigValidationError('Logging configuration is required');
    }
    if (!config.alerting) {
      throw new MonitoringConfigValidationError('Alerting configuration is required');
    }
  }

  private async evaluateRule(rule: AlertRule): Promise<void> {
    try {
      const metrics = await this.getMetrics({
        name: rule.condition.metric,
        startTime: new Date(Date.now() - this.parseDuration(rule.condition.duration)),
        endTime: new Date()
      });

      if (metrics.length === 0) {
        return;
      }

      const latestMetric = metrics[metrics.length - 1];
      const threshold = rule.condition.threshold;
      const operator = rule.condition.operator;

      let shouldAlert = false;
      switch (operator) {
        case '>':
          shouldAlert = latestMetric.value > threshold;
          break;
        case '>=':
          shouldAlert = latestMetric.value >= threshold;
          break;
        case '<':
          shouldAlert = latestMetric.value < threshold;
          break;
        case '<=':
          shouldAlert = latestMetric.value <= threshold;
          break;
        case '==':
          shouldAlert = latestMetric.value === threshold;
          break;
        case '!=':
          shouldAlert = latestMetric.value !== threshold;
          break;
        default:
          throw new AlertEvaluationError(`Invalid operator: ${operator}`);
      }

      if (shouldAlert) {
        await this.createAlert({
          name: rule.name,
          description: rule.description,
          severity: rule.severity as AlertSeverity,
          status: AlertStatus.ACTIVE,
          source: 'alert-rule',
          condition: rule.condition,
          labels: rule.labels,
          annotations: rule.annotations
        });
      }
    } catch (error) {
      throw new AlertEvaluationError(`Failed to evaluate alert rule: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  private parseDuration(duration: string): number {
    const match = duration.match(/^(\d+)([smhd])$/);
    if (!match) {
      throw new AlertEvaluationError(`Invalid duration format: ${duration}`);
    }

    const [, value, unit] = match;
    const numValue = parseInt(value, 10);

    switch (unit) {
      case 's':
        return numValue * 1000;
      case 'm':
        return numValue * 60 * 1000;
      case 'h':
        return numValue * 60 * 60 * 1000;
      case 'd':
        return numValue * 24 * 60 * 60 * 1000;
      default:
        throw new AlertEvaluationError(`Invalid duration unit: ${unit}`);
    }
  }
} 