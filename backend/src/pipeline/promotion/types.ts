export enum EnvironmentType {
  DEVELOPMENT = 'development',
  STAGING = 'staging',
  PRODUCTION = 'production',
  CUSTOM = 'custom'
}

export enum PromotionStrategy {
  MANUAL = 'manual',
  AUTOMATIC = 'automatic',
  SCHEDULED = 'scheduled',
  CONDITIONAL = 'conditional'
}

export enum PromotionStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed',
  CANCELLED = 'cancelled'
}

export interface Environment {
  id: string;
  name: string;
  type: EnvironmentType;
  description: string;
  config: EnvironmentConfig;
  metadata: Record<string, any>;
}

export interface EnvironmentConfig {
  kubernetes?: {
    namespace: string;
    cluster: string;
  };
  variables: Record<string, string>;
  secrets: string[];
  resources: {
    cpu: string;
    memory: string;
    storage?: string;
  };
  replicas: number;
  healthCheck?: {
    path: string;
    port: number;
    initialDelay: number;
    period: number;
  };
}

export interface PromotionRule {
  id: string;
  name: string;
  description: string;
  sourceEnv: string;
  targetEnv: string;
  strategy: PromotionStrategy;
  conditions: PromotionCondition[];
  actions: PromotionAction[];
  metadata: Record<string, any>;
}

export interface PromotionCondition {
  type: 'test' | 'metric' | 'approval' | 'schedule' | 'custom';
  value: any;
  operator: 'equals' | 'greater' | 'less' | 'contains' | 'matches';
  description: string;
}

export interface PromotionAction {
  type: 'deploy' | 'notify' | 'scale' | 'custom';
  config: Record<string, any>;
  order: number;
}

export interface PromotionRequest {
  id: string;
  ruleId: string;
  pipelineId: string;
  version: string;
  sourceEnv: string;
  targetEnv: string;
  status: PromotionStatus;
  startedAt: Date;
  completedAt?: Date;
  metadata: {
    triggeredBy: string;
    reason?: string;
    changes?: Record<string, any>;
  };
}

export interface PromotionHistory {
  id: string;
  requestId: string;
  action: string;
  status: 'success' | 'failure';
  details: Record<string, any>;
  timestamp: Date;
}

// Input types for API
export interface PromotionRuleInput {
  name: string;
  description?: string;
  sourceEnv: string;
  targetEnv: string;
  strategy?: PromotionStrategy;
  conditions: PromotionCondition[];
  actions: PromotionAction[];
  metadata?: Record<string, any>;
}

export interface PromotionRequestInput {
  ruleId?: string;
  pipelineId?: string;
  version?: string;
  sourceEnv: string;
  targetEnv: string;
  metadata?: {
    triggeredBy: string;
    reason?: string;
    changes?: Record<string, any>;
  };
}