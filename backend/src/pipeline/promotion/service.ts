import { PrismaClient } from '@prisma/client';
import { EventEmitter } from 'events';
import {
  Environment,
  PromotionRule,
  PromotionRequest,
  PromotionHistory,
  PromotionStatus,
  PromotionStrategy,
  PromotionCondition,
  PromotionAction
} from './types';
import {
  PromotionValidationError,
  PromotionNotFoundError,
  EnvironmentNotFoundError,
  PromotionConditionError
} from './errors';

export class PromotionService extends EventEmitter {
  private prisma: PrismaClient;

  constructor(prisma: PrismaClient) {
    super();
    this.prisma = prisma;
  }

  async createEnvironment(environment: Environment): Promise<Environment> {
    // Validate environment
    this.validateEnvironment(environment);

    // Create environment
    const created = await this.prisma.environment.create({
      data: {
        id: environment.id,
        name: environment.name,
        type: environment.type,
        description: environment.description,
        config: environment.config,
        metadata: environment.metadata,
      },
    });

    return created;
  }

  async createPromotionRule(rule: PromotionRule): Promise<PromotionRule> {
    // Validate rule
    await this.validatePromotionRule(rule);

    // Create rule
    const created = await this.prisma.promotionRule.create({
      data: {
        id: rule.id,
        name: rule.name,
        description: rule.description,
        sourceEnvId: rule.sourceEnv,
        targetEnvId: rule.targetEnv,
        strategy: rule.strategy,
        conditions: rule.conditions,
        actions: rule.actions,
        metadata: rule.metadata,
      },
    });

    return created;
  }

  async createPromotionRequest(
    ruleId: string,
    pipelineId: string,
    version: string,
    metadata: {
      triggeredBy: string;
      reason?: string;
      changes?: Record<string, any>;
    }
  ): Promise<PromotionRequest> {
    // Get rule
    const rule = await this.prisma.promotionRule.findUnique({
      where: { id: ruleId },
      include: { sourceEnv: true, targetEnv: true },
    });

    if (!rule) {
      throw new PromotionNotFoundError(`Rule ${ruleId} not found`);
    }

    // Create request
    const request = await this.prisma.promotionRequest.create({
      data: {
        ruleId,
        pipelineId,
        version,
        sourceEnvId: rule.sourceEnvId,
        targetEnvId: rule.targetEnvId,
        status: PromotionStatus.PENDING,
        metadata: {
          ...metadata,
          startedAt: new Date(),
        },
      },
    });

    // Process request based on strategy
    if (rule.strategy === PromotionStrategy.AUTOMATIC) {
      await this.processPromotion(request.id);
    }

    return request;
  }

  async processPromotion(requestId: string): Promise<PromotionRequest> {
    const request = await this.prisma.promotionRequest.findUnique({
      where: { id: requestId },
      include: { rule: true },
    });

    if (!request) {
      throw new PromotionNotFoundError(`Request ${requestId} not found`);
    }

    if (request.status !== PromotionStatus.PENDING) {
      throw new PromotionValidationError(`Request ${requestId} is not pending`);
    }

    // Update status
    await this.prisma.promotionRequest.update({
      where: { id: requestId },
      data: { status: PromotionStatus.IN_PROGRESS },
    });

    try {
      // Check conditions
      const conditions = request.rule.conditions as PromotionCondition[];
      for (const condition of conditions) {
        const result = await this.evaluateCondition(condition, request);
        if (!result) {
          throw new PromotionConditionError(`Condition not met: ${condition.description}`);
        }
      }

      // Execute actions
      const actions = request.rule.actions as PromotionAction[];
      for (const action of actions.sort((a, b) => a.order - b.order)) {
        await this.executeAction(action, request);
      }

      // Update status
      const updated = await this.prisma.promotionRequest.update({
        where: { id: requestId },
        data: {
          status: PromotionStatus.COMPLETED,
          completedAt: new Date(),
        },
      });

      // Emit event
      this.emit('promotionCompleted', updated);

      return updated;
    } catch (error) {
      // Update status
      const failed = await this.prisma.promotionRequest.update({
        where: { id: requestId },
        data: {
          status: PromotionStatus.FAILED,
          completedAt: new Date(),
        },
      });

      // Emit event
      this.emit('promotionFailed', failed, error);

      throw error;
    }
  }

  private validateEnvironment(environment: Environment) {
    if (!environment.name) {
      throw new PromotionValidationError('Environment name is required');
    }

    if (!environment.type) {
      throw new PromotionValidationError('Environment type is required');
    }

    if (!environment.config) {
      throw new PromotionValidationError('Environment config is required');
    }
  }

  private async validatePromotionRule(rule: PromotionRule) {
    if (!rule.name) {
      throw new PromotionValidationError('Rule name is required');
    }

    if (!rule.sourceEnv || !rule.targetEnv) {
      throw new PromotionValidationError('Source and target environments are required');
    }

    // Check if environments exist
    const [sourceEnv, targetEnv] = await Promise.all([
      this.prisma.environment.findUnique({ where: { id: rule.sourceEnv } }),
      this.prisma.environment.findUnique({ where: { id: rule.targetEnv } }),
    ]);

    if (!sourceEnv) {
      throw new EnvironmentNotFoundError(`Source environment ${rule.sourceEnv} not found`);
    }

    if (!targetEnv) {
      throw new EnvironmentNotFoundError(`Target environment ${rule.targetEnv} not found`);
    }

    if (!rule.conditions || rule.conditions.length === 0) {
      throw new PromotionValidationError('At least one condition is required');
    }

    if (!rule.actions || rule.actions.length === 0) {
      throw new PromotionValidationError('At least one action is required');
    }
  }

  private async evaluateCondition(
    condition: PromotionCondition,
    request: PromotionRequest
  ): Promise<boolean> {
    switch (condition.type) {
      case 'test':
        return this.evaluateTestCondition(condition, request);
      case 'metric':
        return this.evaluateMetricCondition(condition, request);
      case 'approval':
        return this.evaluateApprovalCondition(condition, request);
      case 'schedule':
        return this.evaluateScheduleCondition(condition, request);
      case 'custom':
        return this.evaluateCustomCondition(condition, request);
      default:
        throw new PromotionConditionError(`Unknown condition type: ${condition.type}`);
    }
  }

  private async executeAction(
    action: PromotionAction,
    request: PromotionRequest
  ): Promise<void> {
    // Record action start
    await this.prisma.promotionHistory.create({
      data: {
        requestId: request.id,
        action: action.type,
        status: 'in_progress',
        details: { config: action.config },
      },
    });

    try {
      switch (action.type) {
        case 'deploy':
          await this.executeDeployAction(action, request);
          break;
        case 'notify':
          await this.executeNotifyAction(action, request);
          break;
        case 'scale':
          await this.executeScaleAction(action, request);
          break;
        case 'custom':
          await this.executeCustomAction(action, request);
          break;
        default:
          throw new PromotionConditionError(`Unknown action type: ${action.type}`);
      }

      // Record success
      await this.prisma.promotionHistory.create({
        data: {
          requestId: request.id,
          action: action.type,
          status: 'success',
          details: { config: action.config },
        },
      });
    } catch (error) {
      // Record failure
      await this.prisma.promotionHistory.create({
        data: {
          requestId: request.id,
          action: action.type,
          status: 'failure',
          details: {
            config: action.config,
            error: error instanceof Error ? error.message : 'Unknown error',
          },
        },
      });

      throw error;
    }
  }

  // Condition evaluators
  private async evaluateTestCondition(
    condition: PromotionCondition,
    request: PromotionRequest
  ): Promise<boolean> {
    // Implement test condition evaluation
    return true;
  }

  private async evaluateMetricCondition(
    condition: PromotionCondition,
    request: PromotionRequest
  ): Promise<boolean> {
    // Implement metric condition evaluation
    return true;
  }

  private async evaluateApprovalCondition(
    condition: PromotionCondition,
    request: PromotionRequest
  ): Promise<boolean> {
    // Implement approval condition evaluation
    return true;
  }

  private async evaluateScheduleCondition(
    condition: PromotionCondition,
    request: PromotionRequest
  ): Promise<boolean> {
    // Implement schedule condition evaluation
    return true;
  }

  private async evaluateCustomCondition(
    condition: PromotionCondition,
    request: PromotionRequest
  ): Promise<boolean> {
    // Implement custom condition evaluation
    return true;
  }

  // Action executors
  private async executeDeployAction(
    action: PromotionAction,
    request: PromotionRequest
  ): Promise<void> {
    // Implement deployment action
  }

  private async executeNotifyAction(
    action: PromotionAction,
    request: PromotionRequest
  ): Promise<void> {
    // Implement notification action
  }

  private async executeScaleAction(
    action: PromotionAction,
    request: PromotionRequest
  ): Promise<void> {
    // Implement scaling action
  }

  private async executeCustomAction(
    action: PromotionAction,
    request: PromotionRequest
  ): Promise<void> {
    // Implement custom action
  }

  // Public methods for controller
  async getPromotionRequest(requestId: string): Promise<PromotionRequest | null> {
    return this.prisma.promotionRequest.findUnique({
      where: { id: requestId },
    });
  }

  async listPromotionRequests(): Promise<PromotionRequest[]> {
    return this.prisma.promotionRequest.findMany({
      orderBy: { createdAt: 'desc' },
    });
  }

  async getPromotionHistory(requestId: string): Promise<PromotionHistory[]> {
    return this.prisma.promotionHistory.findMany({
      where: { requestId },
      orderBy: { createdAt: 'desc' },
    });
  }

  // Alias for controller compatibility
  async processPromotionRequest(requestId: string): Promise<PromotionRequest> {
    return this.processPromotion(requestId);
  }
}