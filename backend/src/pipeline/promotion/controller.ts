import { Request, Response } from 'express';
import { PrismaClient } from '@prisma/client';
import { PromotionService } from './service';
import { PromotionRuleInput, PromotionRequestInput } from './types';

export class PromotionController {
  private promotionService: PromotionService;

  constructor() {
    this.promotionService = new PromotionService(new PrismaClient());
  }

  async createEnvironment(req: Request, res: Response): Promise<void> {
    try {
      const environment = await this.promotionService.createEnvironment(req.body);
      res.status(201).json(environment);
    } catch (error) {
      res.status(500).json({ error: 'Failed to create environment' });
    }
  }

  async createPromotionRule(req: Request, res: Response): Promise<void> {
    try {
      const ruleData = req.body as PromotionRuleInput;
      // Convert input to the expected format
      const rule = await this.promotionService.createPromotionRule({
        id: '', // Will be generated by Prisma
        ...ruleData,
        strategy: ruleData.strategy || 'manual',
        enabled: true,
        createdAt: new Date(),
        updatedAt: new Date(),
      });
      res.status(201).json(rule);
    } catch (error) {
      res.status(500).json({ error: 'Failed to create promotion rule' });
    }
  }

  async createPromotionRequest(req: Request, res: Response): Promise<void> {
    try {
      const requestData = req.body as PromotionRequestInput;
      const request = await this.promotionService.createPromotionRequest(
        requestData.ruleId || '',
        requestData.pipelineId || '',
        requestData.version || '',
        requestData
      );
      res.status(201).json(request);
    } catch (error) {
      res.status(500).json({ error: 'Failed to create promotion request' });
    }
  }

  async processPromotion(req: Request, res: Response): Promise<void> {
    try {
      const { requestId } = req.params;
      const result = await this.promotionService.processPromotionRequest(requestId);
      res.status(200).json(result);
    } catch (error) {
      res.status(500).json({ error: 'Failed to process promotion' });
    }
  }

  async getPromotionRequest(req: Request, res: Response): Promise<void> {
    try {
      const { requestId } = req.params;
      const request = await this.promotionService.getPromotionRequest(requestId);
      res.status(200).json(request);
    } catch (error) {
      res.status(500).json({ error: 'Failed to get promotion request' });
    }
  }

  async listPromotionRequests(req: Request, res: Response): Promise<void> {
    try {
      const requests = await this.promotionService.listPromotionRequests();
      res.status(200).json(requests);
    } catch (error) {
      res.status(500).json({ error: 'Failed to list promotion requests' });
    }
  }

  async executePromotion(req: Request, res: Response): Promise<void> {
    try {
      const { ruleId } = req.params;
      const requestData = req.body as PromotionRequestInput;
      const request = await this.promotionService.createPromotionRequest(
        ruleId,
        requestData.pipelineId || '',
        requestData.version || '',
        {
          ...requestData,
          ruleId
        }
      );
      res.status(200).json(request);
    } catch (error) {
      res.status(500).json({ error: 'Failed to execute promotion' });
    }
  }

  async getPromotionHistory(req: Request, res: Response): Promise<void> {
    try {
      const { requestId } = req.params;
      const history = await this.promotionService.getPromotionHistory(requestId);
      res.status(200).json(history);
    } catch (error) {
      res.status(500).json({ error: 'Failed to get promotion history' });
    }
  }
}