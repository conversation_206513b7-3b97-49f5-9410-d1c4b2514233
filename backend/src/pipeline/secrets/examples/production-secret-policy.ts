import { SecretPolicy } from '../types';

export const productionSecretPolicy: SecretPolicy = {
  id: 'prod-secret-policy',
  name: 'Production Secret Policy',
  description: 'Policy for managing production secrets with strict access controls and rotation requirements',
  rules: [
    {
      name: 'Minimum Length',
      description: 'Secrets must be at least 32 characters long',
      condition: (secret: any) => secret.value.length >= 32,
      errorMessage: 'Secret value must be at least 32 characters long'
    },
    {
      name: 'Complexity Requirements',
      description: 'Secrets must contain uppercase, lowercase, numbers, and special characters',
      condition: (secret: any) => {
        const hasUpperCase = /[A-Z]/.test(secret.value);
        const hasLowerCase = /[a-z]/.test(secret.value);
        const hasNumbers = /\d/.test(secret.value);
        const hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(secret.value);
        return hasUpperCase && hasLowerCase && hasNumbers && hasSpecialChars;
      },
      errorMessage: 'Secret must contain uppercase, lowercase, numbers, and special characters'
    },
    {
      name: 'Rotation Period',
      description: 'Secrets must be rotated every 90 days',
      condition: (secret: any) => {
        if (!secret.rotation?.lastRotated) return true;
        const lastRotated = new Date(secret.rotation.lastRotated);
        const now = new Date();
        const daysSinceRotation = (now.getTime() - lastRotated.getTime()) / (1000 * 60 * 60 * 24);
        return daysSinceRotation <= 90;
      },
      errorMessage: 'Secret must be rotated every 90 days'
    },
    {
      name: 'Access Logging',
      description: 'All secret access must be logged',
      condition: (secret: any) => secret.access?.logging === true,
      errorMessage: 'Secret access logging must be enabled'
    },
    {
      name: 'Team Access',
      description: 'Only production team members can access secrets',
      condition: (secret: any) => {
        const allowedTeams = ['production', 'security', 'devops'];
        return secret.access?.teams?.some((team: any) => allowedTeams.includes(team));
      },
      errorMessage: 'Secret must be accessible only to production, security, or devops teams'
    }
  ],
  metadata: {
    environment: 'production',
    riskLevel: 'high',
    compliance: ['GDPR', 'SOC2', 'ISO27001'],
    tags: ['production', 'security', 'compliance']
  }
}; 