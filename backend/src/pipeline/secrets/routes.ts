import { Router } from 'express';
import { SecretController } from './controller';
import { SecretService } from './service';
import { PrismaClient } from '@prisma/client';
import { authenticate } from '../../middleware/auth';

const router = Router();
const prisma = new PrismaClient();
const secretService = new SecretService(prisma);
const secretController = new SecretController(secretService);

// Create a new secret
router.post('/', authenticate, async (req, res) => {
  await secretController.createSecret(req, res);
});

// Get a secret by ID
router.get('/:secretId', authenticate, async (req, res) => {
  await secretController.getSecret(req, res);
});

// Update a secret
router.put('/:secretId', authenticate, async (req, res) => {
  await secretController.updateSecret(req, res);
});

// Rotate a secret
router.post('/:secretId/rotate', authenticate, async (req, res) => {
  await secretController.rotateSecret(req, res);
});

export default router; 